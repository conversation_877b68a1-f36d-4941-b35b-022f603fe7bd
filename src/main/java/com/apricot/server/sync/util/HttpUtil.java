package com.apricot.server.sync.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.*;
import org.apache.log4j.Logger;
import org.json.JSONObject;

public class HttpUtil {
    static Logger logger = Logger.getLogger(HttpUtil.class);

    private static final String REQUEST_METHOD_GET = "GET";
    private static final String REQUEST_METHOD_PUT = "PUT";
    private static final String REQUEST_METHOD_POST = "POST";

    private static Object base(String requestMethod, String urlPath, String auth, String body) {
        try {
            URL url = new URL(urlPath);
            if (urlPath.startsWith("https")) {
                HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
                return httpsConnect(con, requestMethod, auth, body);
            } else {
                HttpURLConnection con = (HttpURLConnection) url.openConnection();
                return httpConnect(con, requestMethod, auth, body);
            }
        } catch (Exception e) {
            logger.error(e.toString(), e);
        }
        return null;
    }

    private static Object httpConnect(HttpURLConnection con, String requestMethod, String auth, String body) {
        try {
            // Setup HTTP connection
            con.setRequestMethod(requestMethod);
            con.setConnectTimeout(Integer.parseInt(ConfigUtil.PROP.getProperty("connectionTimeout")));
            con.setRequestProperty("Accept-Language", ConfigUtil.PROP.getProperty("acceptLanguage"));
            if (auth != null && !auth.isEmpty()) {
                con.setRequestProperty("Authorization", auth);
            }
            if (REQUEST_METHOD_POST.equals(requestMethod) || REQUEST_METHOD_PUT.equals(requestMethod)) {
                con.setDoOutput(true);
                con.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
                con.setRequestProperty("Accept", "application/json");
                OutputStreamWriter osw = new OutputStreamWriter(con.getOutputStream());
                osw.write(body);
                osw.flush();
                osw.close();
            }

            // Read response
            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            if (con.getResponseCode() == 200) {
                String inputLine;
                StringBuffer content = new StringBuffer();
                while ((inputLine = in.readLine()) != null) {
                    content.append(inputLine);
                }
                in.close();
                JSONObject jo = new JSONObject(content.toString());

                if (jo.get("code") != null && jo.getInt("code") == 20001) {
                    if (jo.has("result")) {
                        return jo.get("result");
                    } else {
                        return jo.get("message") != null ? jo.get("message") : jo.get("code");
                    }
                } else {
                    logger.info("Response Error Code: " + (jo.get("code") != null ? jo.get("code") : ""));
                    logger.info("Response Error Message: " + (jo.get("message") != null ? jo.get("message") : ""));
                }
            } else {
                logger.info("HTTP Error Code: " + con.getResponseCode());
                logger.info("HTTP Error Message: " + con.getResponseMessage());
            }
        } catch (Exception e) {
            logger.error(e.toString(), e);
        }
        return null;
    }

    private static Object httpsConnect(HttpsURLConnection con, String requestMethod, String auth, String body) {
        try {
            // Setup HTTP connection
            trustAllHosts(con);
            con.getHostnameVerifier();
            con.setHostnameVerifier(DO_NOT_VERIFY);
            con.setRequestMethod(requestMethod);
            con.setConnectTimeout(Integer.parseInt(ConfigUtil.PROP.getProperty("connectionTimeout")));
            con.setRequestProperty("Accept-Language", ConfigUtil.PROP.getProperty("acceptLanguage"));
            if (auth != null && !auth.isEmpty()) {
                con.setRequestProperty("Authorization", auth);
            }
            if (REQUEST_METHOD_POST.equals(requestMethod) || REQUEST_METHOD_PUT.equals(requestMethod)) {
                con.setDoOutput(true);
                con.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
                con.setRequestProperty("Accept", "application/json");
                OutputStreamWriter osw = new OutputStreamWriter(con.getOutputStream());
                osw.write(body);
                osw.flush();
                osw.close();
            }

            // Read response
            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            if (con.getResponseCode() == 200) {
                String inputLine;
                StringBuffer content = new StringBuffer();
                while ((inputLine = in.readLine()) != null) {
                    content.append(inputLine);
                }
                in.close();
                JSONObject jo = new JSONObject(content.toString());

                if (jo.get("code") != null && jo.getInt("code") == 20001) {
                    if (jo.has("result")) {
                        return jo.get("result");
                    } else {
                        return jo.get("message") != null ? jo.get("message") : jo.get("code");
                    }
                } else {
                    logger.info("Response Error Code: " + (jo.get("code") != null ? jo.get("code") : ""));
                    logger.info("Response Error Message: " + (jo.get("message") != null ? jo.get("message") : ""));
                }
            } else {
                logger.info("HTTP Error Code: " + con.getResponseCode());
                logger.info("HTTP Error Message: " + con.getResponseMessage());
            }
        } catch (Exception e) {
            logger.error(e.toString(), e);
        }
        return null;
    }

    public static Object get(String urlPath) {
        return base(REQUEST_METHOD_GET, urlPath, ParamUtil.LOGIN_TOKEN, "");
    }

    public static Object post(String urlPath, String body) {
        return base(REQUEST_METHOD_POST, urlPath, ParamUtil.LOGIN_TOKEN, body);
    }

    public static Object put(String urlPath, String body) {
        return base(REQUEST_METHOD_PUT, urlPath, ParamUtil.LOGIN_TOKEN, body);
    }

    private static final TrustManager[] trustAllCerts = new TrustManager[] {
        new X509TrustManager() {
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return new java.security.cert.X509Certificate[] {};
            }

            public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {}

            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {}
        }
    };

    private static final HostnameVerifier DO_NOT_VERIFY = new HostnameVerifier() {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    };

    private static SSLSocketFactory trustAllHosts(HttpsURLConnection connection) {
        SSLSocketFactory oldFactory = connection.getSSLSocketFactory();
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            SSLSocketFactory newFactory = sc.getSocketFactory();
            connection.setSSLSocketFactory(newFactory);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return oldFactory;
    }
}
