package com.apricot.server.sync.util;

import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class ParamUtil {

    static Logger logger = Logger.getLogger(ParamUtil.class);

    public static String LOGIN_TOKEN = "";

    public static long DELTA_TIME_SECONDS = 0;

    public static final String PATIENT = "PATIENT";
    public static final String APPOINTMENT = "APPOINTMENT";

    public static final String CLINIC_TABLE = "CLINICINFORMATIONTABLE";
    public static final String DOCTOR_TABLE = "DOCTORINFORMATIONTABLE";
    public static final String DRUG_TABLE = "DRUGMASTERTABLE";
    public static final String DRUG_REMARKS_TABLE = "DRUGREMARKSTABLE";
    public static final String INVENTORY_TABLE = "INVENTORYTABLE";
    public static final String INVENTORY_REMARKS_TABLE = "INVENTORYREMARKSTABLE";
    public static final String DRUG_TERM_TABLE = "DRUGUSUALTERMTABLE";
    public static final String DRUG_PACKAGE_TABLE = "DRUGPACKAGETABLE";
    public static final String DOSAGE_TABLE = "DOSAGETABLE";
    public static final String FREQUENCY_TABLE = "FREQUENCYTABLE";
    public static final String SUPPLIER_TABLE = "SUPPLIERTABLE";
    public static final String MEDICAL_RECORD_TABLE = "MEDICALRECORDTABLE";
    public static final String PRESCRIPTION_RECORD_TABLE = "PRESCRIPTIONRECORDTABLE";

    public static final String PAYMENT_ITEM_TABLE = "PAYMENTITEMTABLE";
    public static final String BILLING_ITEM_GROUP_TABLE = "BILLINGITEMGROUPTABLE";
    public static final String PAYMENT_ACCOUNT_TABLE = "PAYMENTACCOUNTTABLE";

    public static final String LAB_SETTING_TABLE = "LABORATORYSETTINGTABLE";

    public static final String LAB_MASTER_TABLE = "PATIENTLABORATORYMASTERTABLE";

    public static final String LAB_SLAVE_TABLE = "PATIENTLABORATORYSLAVETABLE";

    public static final String PAYMENT_MASTER_TABLE = "PAYMENTMASTERTABLE";
    public static final String PAYMENT_SLAVE_TABLE = "PAYMENTSLAVETABLE";
    public static final String ACCOUNT_RECEIVABLE_TABLE = "ACCOUNTRECEIVABLETABLE";
    public static final String INCOME_TABLE = "INCOMETABLE";

    public static final String PATIENT_DRUG_ALLERGY_TABLE = "PATIENTDRUGALLERGYTABLE";
    public static final String PATIENT_EXTEND_MEDICAL_RECORD_TABLE = "PATIENTEXTENDMEDICALRECORDTABLE";
    public static final String PATIENT_GLUCOSE_AND_BP_TABLE = "PATIENTGLUCOSEANDBPTABLE";
    public static final String PATIENT_HEALTH_DATA_TABLE = "PATIENTHEALTHDATATABLE";
    public static final String PATIENT_IMMUNIZATION_TABLE = "PATIENTIMMUNIZATIONTABLE";

    public static final String DOCUMENT_TEMPLATE_TABLE = "DOCUMENTTEMPLATETABLE";
    public static final String PATIENT_DOCUMENT_TABLE = "PATIENTDOCUMENTTABLE";
    public static final String PATIENT_IMAGE_DATA_TABLE = "PATIENTIMAGEDATATABLE";

    public static final String MEDICAL_DATA_USUAL_TERM_TABLE = "MEDICALDATAUSUALTERMTABLE";
    public static final String PRINTER_SETTING_TABLE = "PRINTERSETTINGTABLE";

    public static final String PATIENT_TABLE = "PATIENTTABLE";
    public static final String PATIENT_REMOVE_TABLE = "PATIENTREMOVETABLE";
    public static final String PATIENT_EXTEND_TABLE = "PATIENTEXTENDTABLE";
    public static final String PATIENT_COUNTER_TABLE = "PATIENTCOUNTERTABLE";
    public static final String APPOINTMENT_TABLE = "APPOINTMENTTABLE";
    public static final String APPOINTMENT_REMOVE_TABLE = "APPOINTMENTREMOVETABLE";

    public static final String DRUG_STOCK_IN_TABLE = "DRUGSTOCKINTABLE";
    public static final String DRUG_CONSUMPTION_TABLE = "DRUGCONSUMPTIONTABLE";

    public static final String INVENTORY_STOCK_IN_TABLE = "INVENTORYSTOCKINTABLE";
    public static final String INVENTORY_STOCK_OUT_TABLE = "INVENTORYSTOCKOUTTABLE";

    // public static final String TYPE_PATIENT_ID = "EPLUSBOOK_PATIENT_ID";
    // public static final String TYPE_PATIENT_LAST_MODIFIED_DATE = "EPLUSBOOK_PATIENT_LMD";
    public static final String TYPE_PATIENT_EXTEND_REMARK = "REMARKS";

    public static final int PATIENT_WITH_REMARK = 1;
    public static final int PATIENT_WITHOUT_REMARK = 0;

    public static JSONArray MAPPING_CONFIG;
    public static Map<String, String> CLINIC_MAP;
    public static Map<String, String> DOCTOR_MAP;
    public static String DOCTOR_ID = "doctorId";
    public static String DOCTOR_CODE = "doctorCode";
    public static String CLINIC_ID = "clinicId";
    public static String CLINIC_CODE = "clinicCode";
    public static String ID = "id";
    public static String CODE = "code";

    public static final DateTimeFormatter LASTUPDATETIME_DF = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter LASTUPDATETIME_MIN_DF = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    public static final DateTimeFormatter YMD_DF = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static final String SYSTEM_TIME_ZONE_ID_DESC = "Asia/Hong_Kong";
    public static final String SYSTEM_TIME_ZONE_OFFSET_DESC = "+08:00";
    public static final ZoneId SYSTEM_TIME_ZONE_ID = ZoneId.of(SYSTEM_TIME_ZONE_ID_DESC);
    public static final ZoneOffset SYSTEM_TIME_ZONE_OFFSET = ZoneOffset.of(SYSTEM_TIME_ZONE_OFFSET_DESC);
    public static final TimeZone SYSTEM_TIME_ZONE = TimeZone.getTimeZone(SYSTEM_TIME_ZONE_ID_DESC);

    public static final String APPOINTMENT_DIFF_MODIFIED_NON_SYNC = "10";

    public static final int EPB_BOOKING_TYPE_APPOINTMENT = 1;
    public static final int EPB_BOOKING_TYPE_WALK_IN = 2;

    public static final int EPB_BOOKING_STATUS_CANCELLED = -8;
    public static final int EPB_BOOKING_STATUS_BOOKED = 0;
    public static final int EPB_BOOKING_STATUS_CHECK_IN = 1;
    public static final int EPB_BOOKING_STATUS_CONSULT = 2;

    public static final int CS_APPOINTMENT_STATUS_BOOKED = 2;
    public static final int CS_APPOINTMENT_STATUS_CHECK_IN = 1;
    public static final int CS_APPOINTMENT_STATUS_CONSULT = 0;

    public static final int CS_WALK_IN_BOOKING_INTERVAL = 10;

    public static final int CS_PATIENT_SEX_F = 0;
    public static final int CS_PATIENT_SEX_M = 1;

    public static int convertBookingStatus(int status) {
        switch (status) {
            case EPB_BOOKING_STATUS_BOOKED:
                return CS_APPOINTMENT_STATUS_BOOKED;
            case EPB_BOOKING_STATUS_CHECK_IN:
                return CS_APPOINTMENT_STATUS_CHECK_IN;
            case EPB_BOOKING_STATUS_CONSULT:
                return CS_APPOINTMENT_STATUS_CONSULT;
        }
        return -1;
    }

    public static int convertAppointStatus(int status) {
        switch (status) {
            case CS_APPOINTMENT_STATUS_BOOKED:
                return EPB_BOOKING_STATUS_BOOKED;
            case CS_APPOINTMENT_STATUS_CHECK_IN:
                return EPB_BOOKING_STATUS_CHECK_IN;
            case CS_APPOINTMENT_STATUS_CONSULT:
                return EPB_BOOKING_STATUS_CONSULT;
        }
        return -1;
    }

    public static int convertPatientSex(String gender) {
        if (gender.equalsIgnoreCase("M")) {
            return CS_PATIENT_SEX_M;
        } else if (gender.equalsIgnoreCase("F")) {
            return CS_PATIENT_SEX_F;
        }
        return CS_PATIENT_SEX_M;
    }

    public static String convertPatientGender(int sex) {
        if (sex == CS_PATIENT_SEX_M) {
            return "M";
        } else if (sex == CS_PATIENT_SEX_F) {
            return "F";
        }
        return "";
    }

    public static String getDoctorData(String clinicCode, String doctor, String target) {
        if (MAPPING_CONFIG != null) {
            for (int i = 0; i < MAPPING_CONFIG.length(); i++) {
                try {
                    JSONObject jo = MAPPING_CONFIG.getJSONObject(i);
                    if (clinicCode.isEmpty()) {
                        if (jo.getString(DOCTOR_ID).equals(doctor)) {
                            return jo.getString(target);
                        }
                    } else {
                        if (jo.getString(CLINIC_CODE).equals(clinicCode)
                                && jo.getString(DOCTOR_CODE).equals(doctor)) {
                            return jo.getString(target);
                        }
                    }
                } catch (JSONException e) {
                    logger.error(e.toString(), e);
                }
            }
        }
        return "";
    }

    public static String getMatchData(String id, String code) {
        if (MAPPING_CONFIG != null) {
            for (int i = 0; i < MAPPING_CONFIG.length(); i++) {
                try {
                    JSONObject jo = MAPPING_CONFIG.getJSONObject(i);
                    if (!id.isEmpty()) {
                        if (jo.getString(ID).equals(id)) {
                            return jo.getString(ID);
                        }
                    } else {
                        if (jo.getString(CODE).equals(code)) {
                            return jo.getString(CODE);
                        }
                    }
                } catch (JSONException e) {
                    logger.error(e.toString(), e);
                }
            }
        }
        return "";
    }

    public static void initClinicSet() {
        if (MAPPING_CONFIG != null) {
            CLINIC_MAP = new HashMap<>();
            for (int i = 0; i < MAPPING_CONFIG.length(); i++) {
                try {
                    JSONObject jo = MAPPING_CONFIG.getJSONObject(i);
                    if (jo.has(CLINIC_ID) && jo.has(CLINIC_CODE)) {
                        CLINIC_MAP.put(jo.getString(CLINIC_CODE), jo.getString(CLINIC_ID));
                    }
                } catch (Exception e) {
                    logger.error(e.toString(), e);
                }
            }
        }
    }

    public static void initDoctorSet() {
        if (MAPPING_CONFIG != null) {
            DOCTOR_MAP = new HashMap<>();
            for (int i = 0; i < MAPPING_CONFIG.length(); i++) {
                try {
                    JSONObject jo = MAPPING_CONFIG.getJSONObject(i);
                    if (jo.has(DOCTOR_ID) && jo.has(DOCTOR_CODE)) {
                        DOCTOR_MAP.put(jo.getString(DOCTOR_CODE), jo.getString(DOCTOR_ID));
                    }
                } catch (Exception e) {
                    logger.error(e.toString(), e);
                }
            }
        }
    }

    public static String getDoctorCodeById(String doctorId) {
        return getDoctorData("", doctorId, DOCTOR_CODE);
    }

    public static String getDoctorIdByCode(String clinicCode, String doctorCode) {
        return getDoctorData(clinicCode, doctorCode, DOCTOR_ID);
    }

    public static Set<String> getDoctorCodeSet() {
        Set<String> doctorCodeSet = new HashSet<>();
        if (MAPPING_CONFIG != null) {
            for (int i = 0; i < MAPPING_CONFIG.length(); i++) {
                try {
                    JSONObject jo = MAPPING_CONFIG.getJSONObject(i);
                    if (jo.has(DOCTOR_CODE)) {
                        doctorCodeSet.add(jo.getString(DOCTOR_CODE));
                    }
                } catch (JSONException e) {
                    logger.error(e.toString(), e);
                }
            }
        }
        return doctorCodeSet;
    }
}
