package com.apricot.server.sync;

import com.apricot.server.sync.service.SyncService;
import com.apricot.server.sync.util.ConfigUtil;
import com.apricot.server.sync.util.LogUtil;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.util.logging.LogManager;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Orientation;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.HBox;
import javafx.stage.Stage;
import org.apache.log4j.Logger;

public class Main extends Application {
    private Logger logger = Logger.getLogger(Main.class);

    private SyncService service;

    public static void main(String[] args) {
        launch(args);
    }

    @Override
    public void start(Stage stage) {
        LogUtil.init();

        System.setProperty("file.encoding", "UTF-8");
        try {
            Field charset = Charset.class.getDeclaredField("defaultCharset");
            charset.setAccessible(true);
            charset.set(null, null);
        } catch (Exception e) {
            logger.error(e.toString(), e);
        }
        logger.info("Logger is init.");

        ConfigUtil.init();
        service = new SyncService();

        set(stage);
    }

    private void set(Stage stage) {
        stage.setTitle("Vita Migration v0.0.4");

        // Main Job
        HBox initHBox = new HBox();
        initHBox.setPadding(new Insets(15, 12, 15, 12));
        initHBox.setSpacing(10);

        // Main Job
        HBox preHBox = new HBox();
        preHBox.setPadding(new Insets(15, 12, 15, 12));
        preHBox.setSpacing(10);

        //
        Button initializeBtn = new Button();
        initializeBtn.setText("Initialize");
        initializeBtn.setOnAction(event -> service.initialize());

        Button exportDuplicateBtn = new Button();
        exportDuplicateBtn.setText("Export Duplicate");
        exportDuplicateBtn.setOnAction(event -> service.exportDuplicatePatient());

        Button exportBtn = new Button();
        exportBtn.setText("Export Patient");
        exportBtn.setOnAction(event -> service.exportPatient());

        Button importLabBtn = new Button();
        importLabBtn.setText("Import Lab");
        importLabBtn.setOnAction(event -> service.importLab());

        Button importImagingBtn = new Button();
        importImagingBtn.setText("Import Lab Imaging");
        importImagingBtn.setOnAction(event -> service.importImaging());

        Button pathLabBtn = new Button();
        pathLabBtn.setText("Path Lab");
        pathLabBtn.setOnAction(event -> service.importPathLab());

        Button importDrugBtn = new Button();
        importDrugBtn.setText("Import Drug");
        importDrugBtn.setOnAction(event -> service.importDrug());

        Button importInvBtn = new Button();
        importInvBtn.setText("Import Inventory");
        importInvBtn.setOnAction(event -> service.importInventory());

        Button importPatientBtn = new Button();
        importPatientBtn.setText("Import Patient");
        importPatientBtn.setOnAction(event -> service.importPatient());

        Button importNTECPatientBtn = new Button();
        importNTECPatientBtn.setText("importNTECPatient");
        importNTECPatientBtn.setOnAction(event -> {
            try {
                service.importNTECPatient();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        Button updateDrugBtn = new Button();
        updateDrugBtn.setText("updateDrugCostPrice");
        updateDrugBtn.setOnAction(event -> {
            service.updateDrugCostPrice();
        });

        Button importPatientAAFTBtn = new Button();
        importPatientAAFTBtn.setText("importPatientAAFT");
        importPatientAAFTBtn.setOnAction(event -> {
            try {
                service.importPatient_AAFT();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        Button importDrugInterfaceBtn = new Button();
        importDrugInterfaceBtn.setText("importDrugInterface");
        importDrugInterfaceBtn.setOnAction(event -> {
            service.importDrugInterface();
        });

        Button checkMTTBtn = new Button();
        checkMTTBtn.setText("checkMTT");
        checkMTTBtn.setOnAction(event -> {
            service.checkMTT();
        });

        Button importAAFTBtn = new Button();
        importAAFTBtn.setText("importAAFT");
        importAAFTBtn.setOnAction(event -> {
            service.importAAFT();
        });

        initHBox.getChildren()
                .addAll(
                        initializeBtn,
                        exportDuplicateBtn,
                        importDrugInterfaceBtn,
                        exportBtn,
                        importLabBtn,
                        importImagingBtn,
                        importDrugBtn,
                        importInvBtn,
                        pathLabBtn,
                        importPatientBtn,
                        importNTECPatientBtn,
                        updateDrugBtn,
                        importPatientAAFTBtn,
                        checkMTTBtn,
                        importAAFTBtn);

        FlowPane flowPane = new FlowPane();
        flowPane.setOrientation(Orientation.VERTICAL);
        flowPane.getChildren().add(initHBox);
        flowPane.getChildren().add(preHBox);
        stage.setScene(new Scene(flowPane, 800, 450));
        stage.show();

        stage.setOnCloseRequest(arg0 -> {
            LogManager.getLogManager().reset();
            Platform.exit();
            System.exit(0);
        });
    }
}
