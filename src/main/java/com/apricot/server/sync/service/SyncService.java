package com.apricot.server.sync.service;

import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;

import com.apricot.app.aeps.common.domain.Bill;
import com.apricot.app.aeps.common.domain.ClinicPatient;
import com.apricot.app.aeps.common.domain.Company;
import com.apricot.app.aeps.common.domain.DiagnosisDirectory;
import com.apricot.app.aeps.common.domain.Dosage;
import com.apricot.app.aeps.common.domain.Drug;
import com.apricot.app.aeps.common.domain.DrugCaution;
import com.apricot.app.aeps.common.domain.DrugInstr;
import com.apricot.app.aeps.common.domain.Freq;
import com.apricot.app.aeps.common.domain.Inventory;
import com.apricot.app.aeps.common.domain.LabImaging;
import com.apricot.app.aeps.common.domain.LabProvServ;
import com.apricot.app.aeps.common.domain.LabProvider;
import com.apricot.app.aeps.common.domain.LabService;
import com.apricot.app.aeps.common.domain.LocationStock;
import com.apricot.app.aeps.common.domain.PatientGroup;
import com.apricot.app.aeps.common.domain.StockCostDaily;
import com.apricot.app.aeps.common.domain.Unit;
import com.apricot.app.aeps.common.domain.Vendor;
import com.apricot.app.aeps.common.enumeration.PersonalIdTypeEnum;
import com.apricot.app.aeps.common.model.FamilyRelationship;
import com.apricot.app.aeps.common.model.InsuranceModel;
import com.apricot.app.aeps.common.model.LocationStockUnitModel;
import com.apricot.app.aeps.common.model.PersonalIdDetailModel;
import com.apricot.app.aeps.common.model.UnitMatrixModel;
import com.apricot.app.aeps.common.util.Constants;
import com.apricot.app.aeps.common.util.Constants.DI_TYPE;
import com.apricot.app.aeps.common.util.MathUtil;
import com.apricot.server.sync.util.CloudDatabaseUtil;
import com.apricot.server.sync.util.ConfigUtil;
import com.apricot.server.sync.util.ExcelUtil;
import com.apricot.server.sync.util.TimeUtil;
import com.google.gson.Gson;
import com.mongodb.MongoClient;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.types.Decimal128;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;

public class SyncService {

    private Logger logger = Logger.getLogger(SyncService.class);

    private static ObjectId SOURCE_COMPANY_ID;

    private static Document SOURCE_COMPANY_DOC;

    private static MongoDatabase mongoDatabase;

    private Gson gson = new Gson();

    private Map<String, Object> cacheMap = new HashMap<>();

    private Set<String> cacheNotFoundSet = new HashSet<>();

    private Map<String, ObjectId> idCacheMap = new HashMap<>();

    private static String TODAY = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));

    private static String SOURCE;

    public static ObjectId getSourceCompanyId() {
        return SOURCE_COMPANY_ID;
    }

    public static Document getSourceCompanyDoc() {
        return SOURCE_COMPANY_DOC;
    }

    public static MongoDatabase getMongoDatabase() {
        return mongoDatabase;
    }

    public static String getSOURCE() {
        return SOURCE;
    }

    public void initialize() {
        logger.info("Migration is initialing...");
        try {
            String sourceCompanyProp = ConfigUtil.PROP.getProperty("sourceCompany");
            logger.info("Source Company ID: " + sourceCompanyProp);
            SOURCE_COMPANY_ID = new ObjectId(sourceCompanyProp);
            SOURCE_COMPANY_DOC = new Document("comId", SOURCE_COMPANY_ID);

            CodecRegistry pojoCodecRegistry = fromRegistries(
                    MongoClient.getDefaultCodecRegistry(),
                    fromProviders(PojoCodecProvider.builder().automatic(true).build()));
            mongoDatabase = CloudDatabaseUtil.connect().withCodecRegistry(pojoCodecRegistry);

            MongoCollection<Company> companyCol = mongoDatabase.getCollection(Company.COLLECTION_NAME, Company.class);
            Company sourceCompany =
                    companyCol.find(new Document("_id", SOURCE_COMPANY_ID)).first();
            if (sourceCompany == null) {
                logger.error("!!!!! COMPANY NOT FOUND !!!!!");
                return;
            }
            logger.info("From: " + sourceCompany.getCode() + "/" + sourceCompany.getDes());
            SOURCE = sourceCompany.getCode() + "_" + TODAY;
            logger.info("Initialization is completed.");
        } catch (Exception e) {
            logger.error(e.toString(), e);
        }
    }

    public Decimal128 bigDecimalToDecimal128(BigDecimal value) {
        try {
            return value != null ? Decimal128.parse(value.stripTrailingZeros().toPlainString()) : null;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public static ObjectId strToObjId(String id) {
        return StringUtils.isNotBlank(id) && ObjectId.isValid(id) ? new ObjectId(id) : null;
    }

    public static String formatValidHKID(String personalIdentifier) {
        personalIdentifier = personalIdentifier.replaceAll("\\s", "").toUpperCase();
        if (!personalIdentifier.contains("(") || !personalIdentifier.contains(")")) {
            personalIdentifier = personalIdentifier.replaceAll("\\(|\\)", "");
            if (personalIdentifier.length() == 8) {
                if (validateHKID(
                        personalIdentifier.substring(0, 1),
                        personalIdentifier.substring(1, 7),
                        personalIdentifier.substring(7, 8))) {
                    return personalIdentifier.substring(0, 7) + "(" + personalIdentifier.substring(7, 8) + ")";
                }
            } else if (personalIdentifier.length() == 9
                    && validateHKID(
                            personalIdentifier.substring(0, 2),
                            personalIdentifier.substring(2, 8),
                            personalIdentifier.substring(8, 9))) {
                return personalIdentifier.substring(0, 8) + "(" + personalIdentifier.substring(8, 9) + ")";
            }
        }
        return personalIdentifier;
    }

    public static boolean validateHKID(String personalIdentifier) {
        personalIdentifier = personalIdentifier.replaceAll("\\s|\\(|\\)", "").toUpperCase();
        if (personalIdentifier.length() == 8) {
            return validateHKID(
                    personalIdentifier.substring(0, 1),
                    personalIdentifier.substring(1, 7),
                    personalIdentifier.substring(7, 8));
        } else {
            return personalIdentifier.length() == 9
                    ? validateHKID(
                            personalIdentifier.substring(0, 2),
                            personalIdentifier.substring(2, 8),
                            personalIdentifier.substring(8, 9))
                    : false;
        }
    }

    private static boolean validateHKID(String prefix, String serial, String checkdigit) {
        try {
            String prefixU = prefix.toUpperCase();
            long value = 0L;
            if (prefixU.length() == 2) {
                value += (long) ((prefixU.charAt(0) - 55) * 9 + (prefixU.charAt(1) - 55) * 8);
            } else if (prefixU.length() == 1) {
                value += (long) (324 + (prefixU.charAt(0) - 55) * 8);
            }
            for (int i = 0; i < 6; ++i) {
                value += (long) (Integer.parseInt(serial.substring(i, i + 1)) * (7 - i));
            }
            long reminder = value % 11L;
            long valid_checkdigit = 11L - reminder;
            if ("A".equalsIgnoreCase(checkdigit) && valid_checkdigit == 10L) {
                return true;
            } else if ("0".equalsIgnoreCase(checkdigit) && valid_checkdigit == 11L) {
                return true;
            } else {
                return valid_checkdigit == (long) Integer.parseInt(checkdigit);
            }
        } catch (Exception var10) {
            return false;
        }
    }

    private Map<String, String> initI18n(String en, String zhTw) {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isNotBlank(en)) {
            map.put("en", en);
        }
        if (StringUtils.isNotBlank(zhTw)) {
            map.put("zh-tw", zhTw);
            //        map.put("zh-cn", openCC.convert(zhTw));
        }
        return map;
    }

    Map<String, ObjectId> unitMap = new HashMap<>();

    private void preloadUnitByDes() {
        MongoCollection<Unit> col = mongoDatabase.getCollection(Unit.COLLECTION_NAME, Unit.class);
        FindIterable<Unit> findIterable = col.find(new Document("comId", SOURCE_COMPANY_ID));
        for (Unit value : findIterable) {
            String des = value.getDes();
            if (value.getI18n() != null
                    && value.getI18n().containsKey("des")
                    && StringUtils.isNotBlank(value.getI18n().get("des").get("en"))) {
                des = value.getI18n().get("des").get("en");
            }
            unitMap.put(des, value.getId());
        }
    }

    Map<String, ObjectId> vendorMap = new HashMap<>();

    private void preloadVendorByDes() {
        MongoCollection<Vendor> col = mongoDatabase.getCollection(Vendor.COLLECTION_NAME, Vendor.class);
        FindIterable<Vendor> findIterable = col.find(new Document("comId", SOURCE_COMPANY_ID));
        for (Vendor value : findIterable) {
            String des = value.getDes();
            if (value.getI18n() != null
                    && value.getI18n().containsKey("des")
                    && StringUtils.isNotBlank(value.getI18n().get("des").get("en"))) {
                des = value.getI18n().get("des").get("en");
            }
            vendorMap.put(des, value.getId());
        }
    }

    Map<String, ObjectId> dosageMap = new HashMap<>();

    private void preloadDosageByDes() {
        MongoCollection<Dosage> col = mongoDatabase.getCollection(Dosage.COLLECTION_NAME, Dosage.class);
        FindIterable<Dosage> findIterable = col.find(new Document("comId", SOURCE_COMPANY_ID));
        for (Dosage value : findIterable) {
            String des = value.getDes();
            if (value.getI18n() != null
                    && value.getI18n().containsKey("des")
                    && StringUtils.isNotBlank(value.getI18n().get("des").get("en"))) {
                des = value.getI18n().get("des").get("en");
            }
            dosageMap.put(des, value.getId());
        }
    }

    Map<String, ObjectId> freqMap = new HashMap<>();

    private void preloadFreqByDes() {
        MongoCollection<Freq> col = mongoDatabase.getCollection(Freq.COLLECTION_NAME, Freq.class);
        FindIterable<Freq> findIterable = col.find(new Document("comId", SOURCE_COMPANY_ID));
        for (Freq value : findIterable) {
            String des = value.getDes();
            if (value.getI18n() != null
                    && value.getI18n().containsKey("des")
                    && StringUtils.isNotBlank(value.getI18n().get("des").get("en"))) {
                des = value.getI18n().get("des").get("en");
            }
            freqMap.put(des, value.getId());
        }
    }

    Map<String, ObjectId> cautionMap = new HashMap<>();

    private void preloadCautionByDes() {
        MongoCollection<DrugCaution> col = mongoDatabase.getCollection(DrugCaution.COLLECTION_NAME, DrugCaution.class);
        FindIterable<DrugCaution> findIterable = col.find(new Document("comId", SOURCE_COMPANY_ID));
        for (DrugCaution value : findIterable) {
            String des = value.getDes();
            if (value.getI18n() != null
                    && value.getI18n().containsKey("des")
                    && StringUtils.isNotBlank(value.getI18n().get("des").get("en"))) {
                des = value.getI18n().get("des").get("en");
            }
            cautionMap.put(des, value.getId());
        }
    }

    Map<String, ObjectId> instructionMap = new HashMap<>();

    private void preloadInstructionByDes() {
        MongoCollection<DrugInstr> col = mongoDatabase.getCollection(DrugInstr.COLLECTION_NAME, DrugInstr.class);
        FindIterable<DrugInstr> findIterable = col.find(new Document("comId", SOURCE_COMPANY_ID));
        for (DrugInstr value : findIterable) {
            String des = value.getDes();
            if (value.getI18n() != null
                    && value.getI18n().containsKey("des")
                    && StringUtils.isNotBlank(value.getI18n().get("des").get("en"))) {
                des = value.getI18n().get("des").get("en");
            }
            instructionMap.put(des, value.getId());
        }
    }

    Map<String, ObjectId> drugMap = new HashMap<>();

    private void loadByKey(String collectionName, String key, Map<String, ObjectId> map) {
        MongoCollection<Document> col = mongoDatabase.getCollection(collectionName, Document.class);
        FindIterable<Document> findIterable =
                col.find(new Document("comId", SOURCE_COMPANY_ID)).projection(new Document(key, 1).append("_id", 1));
        int cnt = 0;
        for (Document value : findIterable) {
            if (!value.containsKey(key) || StringUtils.isBlank(value.getString(key))) {
                continue;
            }
            map.put(value.getString(key).toUpperCase(), value.getObjectId("_id"));

            cnt++;
            if (cnt % 100000 == 0) {
                logger.info(collectionName + ": " + cnt);
            }
        }
        logger.info(collectionName + ": " + map.size());
    }

    public void importDrug() {
        logger.info("importDrug: Start");
        int i = 0;
        int colNum = 2;
        try {
            MongoCollection<Drug> drugCol = mongoDatabase.getCollection(Drug.COLLECTION_NAME, Drug.class);
            MongoCollection<LocationStock> lsCol =
                    mongoDatabase.getCollection(LocationStock.COLLECTION_NAME, LocationStock.class);
            MongoCollection<StockCostDaily> scdCol =
                    mongoDatabase.getCollection(StockCostDaily.COLLECTION_NAME, StockCostDaily.class);

            SOURCE = "TEMPLATE_" + TODAY;

            preloadUnitByDes();
            preloadInstructionByDes();
            preloadCautionByDes();
            preloadFreqByDes();
            preloadDosageByDes();
            preloadVendorByDes();

            FileInputStream file = new FileInputStream(ConfigUtil.PROP.getProperty("drugFile"));
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                i++;
                if (i < 2) {
                    continue;
                }
                colNum = 0;
                String regNo = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String drugName = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String label = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                //                String qty = ExcelUtil.getRowValue(row, rowIdx).replaceAll("[^0-9.]", "");
                //                rowIdx++;
                String unit = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                //                String cost = ExcelUtil.getRowValue(row, rowIdx);
                //                rowIdx++;
                String price = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String inInject = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String isDD = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String vendor = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String dosage = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String freq = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String day = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String caution = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String instruction = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                colNum++;

                if (StringUtils.isBlank(drugName)) {
                    break;
                }

                ObjectId unitId = null;
                for (String v : unitMap.keySet()) {
                    if (v.toUpperCase().startsWith(unit.toUpperCase())) {
                        unitId = unitMap.get(v);
                    }
                }
                if (unitId == null) {
                    logger.info("Unit not found: " + unit);
                    continue;
                }

                ObjectId freqId = null;
                for (String v : freqMap.keySet()) {
                    if (v.toUpperCase().startsWith(freq.toUpperCase())) {
                        freqId = freqMap.get(v);
                    }
                }
                if (freqId == null) {
                    logger.info("Frequency not found: " + freq);
                    continue;
                }

                ObjectId dosageId = null;
                for (String v : dosageMap.keySet()) {
                    if (v.toUpperCase().startsWith(dosage.toUpperCase())) {
                        dosageId = dosageMap.get(v);
                    }
                }
                if (dosageId == null) {
                    logger.info("Dosage not found: " + dosage);
                    continue;
                }

                String[] cautions = caution.split("\r\n|\r|\n");
                for (String caut : cautions) {
                    ObjectId cautionId = null;
                    for (String v : cautionMap.keySet()) {
                        if (v.toUpperCase().startsWith(caut.toUpperCase())) {
                            cautionId = cautionMap.get(v);
                            break;
                        }
                    }
                    if (cautionId == null) {
                        logger.info("Caution not found: " + caut);
                    }
                }

                String[] instructions = instruction.split("\r\n|\r|\n");
                for (String instr : instructions) {
                    ObjectId id = null;
                    for (String v : instructionMap.keySet()) {
                        if (v.toUpperCase().startsWith(instr.toUpperCase())) {
                            id = instructionMap.get(v);
                            break;
                        }
                    }
                    if (id == null) {
                        logger.info("Instruction not found: " + instr);
                    }
                }

                ObjectId vendorId = null;
                for (String v : vendorMap.keySet()) {
                    if (v.toUpperCase().startsWith(vendor.toUpperCase())) {
                        vendorId = vendorMap.get(v);
                    }
                }
                if (vendorId == null) {
                    logger.info("Vendor not found:" + vendor);
                    continue;
                }

                Drug drug = new Drug();
                drug.setId(ObjectId.get());
                drug.setComId(SOURCE_COMPANY_ID);
                drug.setCode(StringUtils.leftPad(i + "", 5, "0"));
                drug.setDes(drugName.trim());
                drug.setIsApplicable(true);
                drug.setSource(SOURCE);

                //                drug.setDrugCode(regNo.replaceAll("[^0-9]", ""));
                drug.setDrugLabel(label);
                drug.setUnitId(unitId);
                UnitMatrixModel unitMatrixModel = new UnitMatrixModel();
                unitMatrixModel.setUnitId(unitId);
                unitMatrixModel.setIsStockUnit(true);
                unitMatrixModel.setIsPurchaseUnit(true);
                unitMatrixModel.setIsSalesUnit(true);
                unitMatrixModel.setIsPrescribeUnit(true);
                unitMatrixModel.setStockRatioD(1);
                unitMatrixModel.setStockRatioN(1);
                drug.setUnitMatrixs(Arrays.asList(unitMatrixModel));

                if ("Y".equalsIgnoreCase(inInject)) {
                    drug.setIsInjection(true);
                }

                if ("Y".equalsIgnoreCase(isDD)) {
                    drug.setIsDangerous(true);
                }

                if (StringUtils.isNotBlank(price)) {
                    LocationStock locationStock = new LocationStock();
                    locationStock.setComId(SOURCE_COMPANY_ID);
                    locationStock.setSource(SOURCE);
                    locationStock.setDiType(Constants.DI_TYPE.DRUG);
                    locationStock.setCreatedDate(Instant.now());
                    locationStock.setDrugId(drug.getId());
                    //                    locationStock.setLocId(new ObjectId("66875993608f6b0009bc3791"));

                    //                locationStock.setAvgCost(Decimal128.parse(cost));
                    locationStock.setAvgCost(Decimal128.POSITIVE_ZERO);
                    //                    locationStock.setQty(Decimal128.parse(qty));
                    locationStock.setTtlCost(MathUtil.multiply(locationStock.getQty(), locationStock.getAvgCost()));

                    //                LocationStockUnitModel lsum = new LocationStockUnitModel();
                    //                lsum.setUnitId(unitId);
                    //                lsum.setUp(Decimal128.parse(price));
                    //                locationStock.setLocationStockUnits(new LinkedList<>(Arrays.asList(lsum)));

                    //                ItemBatchModel ibm = new ItemBatchModel();
                    //                ibm.setExpDate(exp);
                    //                ibm.setExpTime(TimeUtil.parseInst(exp));
                    //                locationStock.setBatches(new LinkedList<>(Arrays.asList(ibm)));

                    //                    StockCostDaily scd = new StockCostDaily();
                    //                    scd.setCalDate("2024-07-18");
                    //                    scd.setCalTime(TimeUtil.parseInst(scd.getCalDate()));
                    //
                    //                    scd.setComId(locationStock.getComId());
                    //                    scd.setSource(locationStock.getSource());
                    //                    scd.setDiType(locationStock.getDiType());
                    //                    scd.setCreatedDate(Instant.now());
                    //                    scd.setDrugId(locationStock.getDrugId());
                    //                    scd.setLocId(locationStock.getLocId());
                    //                    scd.setAvgCost(locationStock.getAvgCost());
                    //                    scd.setQty(locationStock.getQty());
                    //                    scd.setTtlCost(locationStock.getTtlCost());
                    //                    scd.setLocationStockUnits(locationStock.getLocationStockUnits());
                    //                    scd.setBatches(locationStock.getBatches());
                }

                drugCol.insertOne(drug);
                //                lsCol.insertOne(locationStock);
                //                scdCol.insertOne(scd);
            }
            logger.info("Total: " + i);
        } catch (Exception e) {
            logger.error("Row " + i + "/" + colNum + " : " + e.getMessage(), e);
        }
        logger.info("importDrug: End");
    }

    public void updateDrugCostPrice() {
        logger.info("updateDrugCostPrice: Start");
        int i = 0;
        try {
            MongoCollection<Drug> drugCol = mongoDatabase.getCollection(Drug.COLLECTION_NAME, Drug.class);
            MongoCollection<LocationStock> lsCol =
                    mongoDatabase.getCollection(LocationStock.COLLECTION_NAME, LocationStock.class);
            MongoCollection<StockCostDaily> scdCol =
                    mongoDatabase.getCollection(StockCostDaily.COLLECTION_NAME, StockCostDaily.class);

            SOURCE = "TEMPLATE_" + TODAY;

            //            preloadUnitByDes();
            //            preloadInstructionByDes();
            //            preloadCautionByDes();
            //            preloadFreqByDes();
            //            preloadDosageByDes();
            //            preloadVendorByDes();
            loadByKey(Drug.COLLECTION_NAME, "code", drugMap);

            FileInputStream file = new FileInputStream(ConfigUtil.PROP.getProperty("drugFile"));
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                i++;
                if (i < 4) {
                    continue;
                }
                String code = ExcelUtil.getRowValue(row, 0);
                String qty = ExcelUtil.getRowValue(row, 1);
                String cost = ExcelUtil.getRowValue(row, 2);
                String price = ExcelUtil.getRowValue(row, 3);

                if (StringUtils.isBlank(code)) {
                    continue;
                }
                if (!drugMap.containsKey(code)) {
                    logger.info("Not found: " + code);
                    continue;
                }

                lsCol.updateOne(
                        new Document("comId", SOURCE_COMPANY_ID)
                                .append("drugId", drugMap.get(code))
                                .append("locationStockUnits.unitId", new Document("$exists", true)),
                        new Document(
                                "$set",
                                new Document("avgCost", Decimal128.parse(cost))
                                        .append("qty", Decimal128.parse(qty))
                                        .append(
                                                "ttlCost",
                                                MathUtil.multiply(Decimal128.parse(qty), Decimal128.parse(cost)))
                                        .append("locationStockUnits.$.up", Decimal128.parse(price))));

                StockCostDaily scd = new StockCostDaily();
                scd.setCalDate("2025-03-09");
                scd.setCalTime(TimeUtil.parseInst(scd.getCalDate()));

                scd.setComId(new ObjectId("67502945b114603f2d0e54b7"));
                scd.setSource(SOURCE);
                scd.setDiType("drug");
                scd.setCreatedDate(Instant.now());
                scd.setDrugId(drugMap.get(code));
                scd.setLocId(new ObjectId("67502946b114603f2d0e553a"));
                scd.setAvgCost(Decimal128.parse(cost));
                scd.setQty(Decimal128.parse(qty));
                scd.setTtlCost(MathUtil.multiply(Decimal128.parse(qty), Decimal128.parse(cost)));
                //                scd.setLocationStockUnits(locationStock.getLocationStockUnits());
                //                scd.setBatches(locationStock.getBatches());
                scdCol.insertOne(scd);
                //                scdCol.insertOne(
                //                        new Document("comId", SOURCE_COMPANY_ID)
                //                            .append("calDate", LocalDate.now().toString())
                //                                .append("drugId", drugMap.get(code))
                //                                .append("locationStockUnits.unitId", new Document("$exists", true)),
                //                        new Document(
                //                                "$set",
                //                                new Document("avgCost", Decimal128.parse(cost))
                //                                        .append("qty", Decimal128.parse(qty))
                //                                    .append("ttlCost", MathUtil.multiply(Decimal128.parse(qty),
                // Decimal128.parse(cost)))
                //                                        .append("locationStockUnits.$.up", Decimal128.parse(price))));
            }
            logger.info("Total: " + i);
        } catch (Exception e) {
            logger.error("Row " + i + " : " + e.getMessage(), e);
        }
        logger.info("updateDrugCostPrice: End");
    }

    public void importPatient_AAFT() throws IOException {
        logger.info("importPatient_AAFT: Start");
        int rowNum = 0;
        MongoCollection<ClinicPatient> cpCol =
                mongoDatabase.getCollection(ClinicPatient.COLLECTION_NAME, ClinicPatient.class);

        Map<String, ObjectId> pgMap = new HashMap<>();
        loadByKey(PatientGroup.COLLECTION_NAME, "des", pgMap);

        List<ClinicPatient> cpList = new ArrayList<>();
        FileInputStream file =
                new FileInputStream("C:\\Project\\_Data\\_AAFT\\Import_patient_list_05032025_Updated.xlsx");
        Workbook workbook = new XSSFWorkbook(file);
        Sheet sheet = workbook.getSheetAt(0);
        for (Row row : sheet) {
            try {
                rowNum = row.getRowNum();
                String key = rowNum + "";
                if (rowNum < 3) {
                    continue;
                }

                int colNum = 0;
                String code = ExcelUtil.getRowValue(row, colNum);
                if (StringUtils.isBlank(code)) {
                    continue;
                }
                colNum++;
                colNum++;
                String caseCode = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String hkid = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String engName = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String chiName = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                Instant dob = ExcelUtil.getRowDate(row, colNum);
                colNum++;
                String gender = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String phoneNum = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String contactPerson = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String email = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String address = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                colNum++;
                String medicalHistory = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String group = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                colNum++;
                String remarks = ExcelUtil.getRowValue(row, colNum);

                ClinicPatient clinicPatient = new ClinicPatient();
                clinicPatient.setComId(SOURCE_COMPANY_ID);
                clinicPatient.setSource(SOURCE);
                clinicPatient.setSourceKey(key);
                clinicPatient.setCreatedDate(Instant.now());
                clinicPatient.setVerifyTime(Instant.now());
                clinicPatient.setVerifyStatus(1);

                clinicPatient.setCode(code);
                if (StringUtils.isNotBlank(hkid)) {
                    if (validateHKID(hkid)) {
                        clinicPatient.setIdentifierType(PersonalIdTypeEnum.HK_IDCARD);
                    }
                    clinicPatient.setPersonalIdentifier(hkid);
                }
                if (StringUtils.isNotBlank(caseCode) && !"No case no.".equalsIgnoreCase(caseCode)) {
                    PersonalIdDetailModel pid = new PersonalIdDetailModel();
                    pid.setEleId(ObjectId.get());
                    pid.setIdentifierType(PersonalIdTypeEnum.OTH);
                    pid.setPersonalIdentifier(caseCode);
                    clinicPatient.setOtherPersonalIds(Arrays.asList(pid));
                }
                if (dob != null) {
                    clinicPatient.setDateOfBirth(TimeUtil.parseLocalDate(dob).toString());
                }
                clinicPatient.setGender(gender);
                clinicPatient.setFirstName(engName);
                clinicPatient.setFullName(engName.toUpperCase().replaceAll(" ", ""));
                clinicPatient.setChiFirstName(chiName);
                clinicPatient.setChiFullName(chiName.replaceAll(" ", ""));
                clinicPatient.setPhoneNum(phoneNum.trim());
                clinicPatient.setEmail(email.trim());
                FamilyRelationship fr = new FamilyRelationship();
                fr.setMember(contactPerson);
                clinicPatient.setEmergencyContact(fr);
                clinicPatient.setAddress(address);
                clinicPatient.setMedicalHistory(medicalHistory);
                clinicPatient.setRemarkByDoctor(remarks);

                if (StringUtils.isNotBlank(group)) {
                    String[] gp = group.split(",|\\+");
                    HashSet<ObjectId> pgSet = new HashSet<>();
                    for (String s : gp) {
                        s = s.trim();
                        ObjectId pgId = null;
                        for (Entry<String, ObjectId> entry : pgMap.entrySet()) {
                            String k = entry.getKey().toUpperCase();
                            ObjectId v = entry.getValue();
                            if (k.equalsIgnoreCase(s) || s.toUpperCase().contains(k)) {
                                pgId = v;
                                break;
                            }
                        }
                        if (pgId == null) {
                            logger.info(s);
                        } else {
                            pgSet.add(pgId);
                        }
                    }
                    clinicPatient.setGroupIds(pgSet);
                    clinicPatient.setRemarkByDoctor((clinicPatient.getRemarkByDoctor() + "\r\n" + group).trim());
                }
                cpCol.insertOne(clinicPatient);
            } catch (Exception e) {
                logger.error("Row " + rowNum + " : " + e.getMessage(), e);
            }
        }
        logger.info("importPatient_AAFT: Total " + cpList.size());
        //                cpCol.insertMany(cpList);
        logger.info("importPatient_AAFT: End");
    }

    public void importInventory() {
        logger.info("importInventory: Start");
        int i = 0;
        int colNum = 0;
        try {
            MongoCollection<Inventory> invCol = mongoDatabase.getCollection(Inventory.COLLECTION_NAME, Inventory.class);
            MongoCollection<LocationStock> lsCol =
                    mongoDatabase.getCollection(LocationStock.COLLECTION_NAME, LocationStock.class);
            MongoCollection<StockCostDaily> scdCol =
                    mongoDatabase.getCollection(StockCostDaily.COLLECTION_NAME, StockCostDaily.class);

            MongoCollection<Unit> unitCol = mongoDatabase.getCollection(Unit.COLLECTION_NAME, Unit.class);
            FindIterable<Unit> unitFindIterable = unitCol.find(new Document("comId", SOURCE_COMPANY_ID));
            Map<String, ObjectId> unitMap = new HashMap<>();
            for (Unit unit : unitFindIterable) {
                unitMap.put(unit.getI18n().get("des").get("en"), unit.getId());
                unitMap.put(unit.getI18n().get("des").get("zh-tw"), unit.getId());
            }

            FileInputStream file = new FileInputStream(ConfigUtil.PROP.getProperty("invFile"));
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() < 3) {
                    continue;
                }
                colNum = 1;
                String code = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String name = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String unit = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String qty = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String cost = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String price = ExcelUtil.getRowValue(row, colNum);

                if (StringUtils.isBlank(name)) {
                    break;
                }

                ObjectId unitId = null;
                for (String v : unitMap.keySet()) {
                    if (v.toUpperCase().startsWith(unit.toUpperCase())) {
                        unitId = unitMap.get(v);
                    }
                }
                if (unitId == null) {
                    logger.info(row.getRowNum() + ": No unit " + unit);
                    continue;
                }

                Inventory inventory = new Inventory();
                inventory.setId(ObjectId.get());
                inventory.setComId(SOURCE_COMPANY_ID);
                inventory.setCode(code);
                inventory.setDes(name.trim());
                inventory.setSource(SOURCE);

                inventory.setUnitId(unitId);
                UnitMatrixModel unitMatrixModel = new UnitMatrixModel();
                unitMatrixModel.setUnitId(unitId);
                unitMatrixModel.setIsStockUnit(true);
                unitMatrixModel.setIsPurchaseUnit(true);
                unitMatrixModel.setIsSalesUnit(true);
                unitMatrixModel.setIsPrescribeUnit(true);
                unitMatrixModel.setStockRatioD(1);
                unitMatrixModel.setStockRatioN(1);
                inventory.setUnitMatrixs(Arrays.asList(unitMatrixModel));

                LocationStock locationStock = new LocationStock();
                locationStock.setComId(SOURCE_COMPANY_ID);
                locationStock.setSource(SOURCE);
                locationStock.setDiType(DI_TYPE.INVENTORY);
                locationStock.setCreatedDate(Instant.now());
                locationStock.setInvId(inventory.getId());
                locationStock.setLocId(new ObjectId("66cf394d0fc6052acbb295d9"));

                locationStock.setAvgCost(Decimal128.parse(cost));
                locationStock.setQty(Decimal128.parse(qty));
                locationStock.setTtlCost(MathUtil.multiply(locationStock.getQty(), locationStock.getAvgCost()));

                LocationStockUnitModel lsum = new LocationStockUnitModel();
                lsum.setUnitId(unitId);
                lsum.setUp(Decimal128.parse(price));
                locationStock.setLocationStockUnits(new LinkedList<>(Arrays.asList(lsum)));

                //                ItemBatchModel ibm = new ItemBatchModel();
                //                ibm.setExpDate(exp);
                //                ibm.setExpTime(TimeUtil.parseInst(exp));
                //                locationStock.setBatches(new LinkedList<>(Arrays.asList(ibm)));

                StockCostDaily scd = new StockCostDaily();
                scd.setCalDate("2024-09-10");
                scd.setCalTime(TimeUtil.parseInst(scd.getCalDate()));

                scd.setComId(locationStock.getComId());
                scd.setSource(locationStock.getSource());
                scd.setDiType(locationStock.getDiType());
                scd.setCreatedDate(Instant.now());
                scd.setDrugId(locationStock.getDrugId());
                scd.setLocId(locationStock.getLocId());
                scd.setAvgCost(locationStock.getAvgCost());
                scd.setQty(locationStock.getQty());
                scd.setTtlCost(locationStock.getTtlCost());
                scd.setLocationStockUnits(locationStock.getLocationStockUnits());
                scd.setBatches(locationStock.getBatches());

                invCol.insertOne(inventory);
                lsCol.insertOne(locationStock);
                scdCol.insertOne(scd);
                i++;
            }
            logger.info("Total: " + i);
        } catch (Exception e) {
            logger.error("Row " + i + "/" + colNum + " : " + e.getMessage(), e);
        }
        logger.info("importInventory: End");
    }

    public void exportPatient() {
        MongoCollection<ClinicPatient> cpCol =
                mongoDatabase.getCollection(ClinicPatient.COLLECTION_NAME, ClinicPatient.class);
        MongoCollection<Document> billCol = mongoDatabase.getCollection(Bill.COLLECTION_NAME, Document.class);
        FindIterable<Document> billFind = billCol.find(new Document("comId", SOURCE_COMPANY_ID)
                        .append("isRemoved", new Document("$ne", true))
                        .append("isVoid", new Document("$ne", true))
                        .append("voidRefId", null))
                .projection(new Document("billTime", 1).append("cpId", 1));
        Map<String, Instant> billMap = new HashMap<>();
        for (Document bill : billFind) {
            String cpId = bill.getObjectId("cpId").toString();
            Instant billTime = bill.getDate("billTime").toInstant();
            if (!billMap.containsKey(cpId)) {
                billMap.put(cpId, billTime);
            }
            if (billTime.isAfter(billMap.get(cpId))) {
                billMap.put(cpId, billTime);
            }
        }
        logger.info("Bill: " + billMap.size() + " bill");

        // Create a workbook
        Workbook workbook = new XSSFWorkbook();

        // Create a sheet
        Sheet sheet = workbook.createSheet("Patient");

        // Create header row
        Row headerRow = sheet.createRow(0);
        int i = 0;
        headerRow.createCell(i++).setCellValue("CODE");
        headerRow.createCell(i++).setCellValue("NAME");
        headerRow.createCell(i++).setCellValue("ADDRESS");
        headerRow.createCell(i++).setCellValue("PHONE");
        headerRow.createCell(i++).setCellValue("LAST BILLED TIME");

        FindIterable<ClinicPatient> findIterable = cpCol.find(new Document("comId", SOURCE_COMPANY_ID)
                        .append("isRemoved", new Document("$ne", true))
                        .append("verifyStatus", 1))
                .sort(new Document("code", 1));
        // Write data to sheet
        int rowCount = 1;
        for (ClinicPatient clinicPatient : findIterable) {
            if (rowCount % 10000 == 0) logger.info("rowCount: " + rowCount);

            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            Cell cell = row.createCell(columnCount++);
            cell.setCellValue(clinicPatient.getCode());

            cell = row.createCell(columnCount++);
            cell.setCellValue(clinicPatient.getEnglishName());

            cell = row.createCell(columnCount++);
            cell.setCellValue(clinicPatient.getAddress());

            cell = row.createCell(columnCount++);
            cell.setCellValue(clinicPatient.getPhoneNum());

            cell = row.createCell(columnCount++);
            if (billMap.containsKey(clinicPatient.getId().toHexString()))
                cell.setCellValue(TimeUtil.parseLocalDate(
                                billMap.get(clinicPatient.getId().toHexString()))
                        .toString());
        }

        // Write the output to a file
        try (FileOutputStream outputStream = new FileOutputStream("241218_Export.xlsx")) {
            workbook.write(outputStream);
            logger.info("Data written successfully.");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private Map getDataMap(String collectionName, String key) {
        MongoCollection<Document> collection = mongoDatabase.getCollection(collectionName, Document.class);
        FindIterable<Document> findIterable = collection.find(new Document("comId", SOURCE_COMPANY_ID));
        Map<String, ObjectId> map = new HashMap<>();
        for (Document document : findIterable) {
            map.put(document.getString(key), document.getObjectId("_id"));
        }
        return map;
    }

    public void importICD11() throws FileNotFoundException {
        logger.info("Import ICD11: Start");

        MongoCollection<DiagnosisDirectory> ddCol =
                mongoDatabase.getCollection(DiagnosisDirectory.COLLECTION_NAME, DiagnosisDirectory.class);

        Instant now = Instant.now();
        JSONArray jsonArray;
        try (FileReader reader = new FileReader(ConfigUtil.PROP.getProperty("icd11File"))) {
            jsonArray = new JSONArray(new JSONTokener(reader));
            List<DiagnosisDirectory> ddList = new ArrayList<>();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String schema = jsonObject.getString("schema");
                String icdCode = jsonObject.getString("icdCode");
                String des = jsonObject.getString("des");

                DiagnosisDirectory dd = new DiagnosisDirectory();
                dd.setComId(SOURCE_COMPANY_ID);
                dd.setCode(icdCode);
                dd.setDes(des);
                dd.setCreatedDate(now);
                dd.setIcdCode(icdCode.replace(".", ""));
                dd.setLastModifiedDate(now);
                dd.setScheme(schema);
                dd.setSource("241120");

                ddList.add(dd);
            }
            ddCol.insertMany(ddList);
            logger.info("Total: " + ddList.size());
        } catch (IOException | JSONException e) {
            logger.error("Error reading JSON file: " + e.getMessage(), e);
        }
        logger.info("Import ICD11: End");
    }

    /**
     * VCS-4295 [NTEC] Import patient data
     */
    public void importNTECPatient() throws IOException {
        logger.info("importNTECPatient: Start");
        int rowNum = 0;
        MongoCollection<ClinicPatient> cpCol =
                mongoDatabase.getCollection(ClinicPatient.COLLECTION_NAME, ClinicPatient.class);

        Set<String> codeSet = new HashSet<>();
        MongoCollection<Document> cpDocCol = mongoDatabase.getCollection(ClinicPatient.COLLECTION_NAME, Document.class);
        FindIterable<Document> cpDocFi = cpDocCol.find(
                        new Document("comId", SOURCE_COMPANY_ID).append("isRemoved", new Document("$ne", true)))
                .projection(new Document("code", 1));
        for (Document value : cpDocFi) {
            codeSet.add(value.getString("code"));
        }
        logger.info("Existing: " + codeSet.size());

        List<ClinicPatient> cpList = new ArrayList<>();
        FileInputStream file = new FileInputStream(ConfigUtil.PROP.getProperty("ntecPatientFile"));
        Workbook workbook = new XSSFWorkbook(file);
        Sheet sheet = workbook.getSheetAt(0);
        for (Row row : sheet) {
            try {
                rowNum = row.getRowNum();
                String key = rowNum + "";
                if (rowNum < 1) {
                    continue;
                }

                int colNum = 0;
                String code = ExcelUtil.getRowValue(row, colNum);
                if (StringUtils.isBlank(code) || codeSet.contains(code)) {
                    continue;
                }
                colNum++;
                String hkid = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                colNum++;
                Instant dob = ExcelUtil.getRowDate(row, colNum);
                colNum++;
                String engName = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String chiName = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String phoneNum = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String remarks1 = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String remarks2 = ExcelUtil.getRowValue(row, colNum);

                ClinicPatient clinicPatient = new ClinicPatient();
                clinicPatient.setComId(SOURCE_COMPANY_ID);
                clinicPatient.setSource(SOURCE);
                clinicPatient.setSourceKey(key);
                clinicPatient.setCreatedDate(Instant.now());
                clinicPatient.setVerifyTime(Instant.now());
                clinicPatient.setVerifyStatus(1);

                clinicPatient.setCode(code);
                clinicPatient.setPersonalIdentifier(code);
                if (dob != null) {
                    clinicPatient.setDateOfBirth(TimeUtil.parseLocalDate(dob).toString());
                }
                clinicPatient.setGender("other");
                clinicPatient.setFirstName(engName);
                clinicPatient.setFullName(engName.toUpperCase().replaceAll(" ", ""));
                clinicPatient.setChiFirstName(chiName);
                clinicPatient.setChiFullName(chiName.replaceAll(" ", ""));
                clinicPatient.setPhoneNum(phoneNum.trim());
                clinicPatient.setRemarkByDoctor((remarks1 + "\r\n" + remarks2).trim());

                cpList.add(clinicPatient);
            } catch (Exception e) {
                logger.error("Row " + rowNum + " : " + e.getMessage(), e);
            }
        }
        logger.info("importNTECPatient: Total " + cpList.size());
        cpCol.insertMany(cpList);
        logger.info("importNTECPatient: End");
    }

    public void importAAFT() {
        logger.info("Import AAFT: Start");
        int i = 0;
        int colNum = 2;
        try {
//            MongoCollection<LabService> lsCol =
//                mongoDatabase.getCollection(LabService.COLLECTION_NAME, LabService.class);
//            MongoCollection<LabProvServ> lpsCol =
//                mongoDatabase.getCollection(LabProvServ.COLLECTION_NAME, LabProvServ.class);
//
//            List<LabService> lsList = new ArrayList<>();
//            List<LabProvServ> lpsList = new ArrayList<>();

            FileInputStream file = new FileInputStream("C:/Project/_Data/AAFT/20250718 - Client Information & Case Information List (1).xlsx");
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheet("Client Info (Batch 1)");
            for (Row row : sheet) {
                i++;
                if (i < 3) {
                    continue;
                }
                String key = row.getRowNum() + "";

                colNum = 0;
                String caseCode = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String patientCode = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String hkid = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String engName = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String chiName = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String dob = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String gender = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String phoneNum = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                colNum++;
                colNum++;
                colNum++;
                colNum++;
                colNum++;
                colNum++;
                String contactPerson = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String email = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String address = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String doctor = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String medicalHistory = ExcelUtil.getRowValue(row, colNum);

                logger.info(row.getRowNum() + " " + caseCode + " " + patientCode + " " + hkid + " " + engName + " " + chiName + " " + dob + " " + gender + " " + phoneNum + " " + contactPerson + " " + email + " " + address + " " + doctor + " " + medicalHistory);
            }
            logger.info("Total: " + i);
        } catch (Exception e) {
            logger.error("Row " + i + "/" + colNum + " : " + e.getMessage(), e);
        }
        logger.info("Import AAFT: End");
    }


    public void importLab() {
        logger.info("Import Lab: Start");
        int i = 0;
        int rowIdx = 2;
        try {
            ObjectId labProviderId = new ObjectId(ConfigUtil.PROP.getProperty("labProviderId"));

            MongoCollection<LabService> lsCol =
                    mongoDatabase.getCollection(LabService.COLLECTION_NAME, LabService.class);
            MongoCollection<LabProvServ> lpsCol =
                    mongoDatabase.getCollection(LabProvServ.COLLECTION_NAME, LabProvServ.class);

            List<LabService> lsList = new ArrayList<>();
            List<LabProvServ> lpsList = new ArrayList<>();

            FileInputStream file = new FileInputStream(ConfigUtil.PROP.getProperty("labFile"));
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                i++;
                if (i < 2) {
                    continue;
                }
                rowIdx = 0;
                String code = ExcelUtil.getRowValue(row, rowIdx);
                rowIdx++;
                String name = ExcelUtil.getRowValue(row, rowIdx);
                rowIdx++;
                rowIdx++;
                String cost = ExcelUtil.getRowValue(row, rowIdx);
                rowIdx++;
                String price = ExcelUtil.getRowValue(row, rowIdx);
                //                rowIdx++;
                //                String instruction = ExcelUtil.getRowValue(row, rowIdx);

                String key = row.getRowNum() + "";

                LabService labService = new LabService();
                labService.setId(ObjectId.get());
                labService.setDes(name.trim());
                labService.setCode(code);
                labService.setComId(SOURCE_COMPANY_ID);
                labService.setSource(SOURCE);
                labService.setSourceKey(key);
                labService.setCreatedDate(Instant.now());
                lsList.add(labService);

                LabProvServ labProvServ = new LabProvServ();
                labProvServ.setProviderId(labProviderId);
                labProvServ.setServiceId(labService.getId());
                labProvServ.setCost(Decimal128.parse(cost));
                labProvServ.setPrice(Decimal128.parse(price));
                //                labProvServ.setInstructions(instruction);
                //                labProvServ.setRemarks("");
                labProvServ.setComId(SOURCE_COMPANY_ID);
                labProvServ.setSource(SOURCE);
                labProvServ.setSourceKey(key);
                labProvServ.setCreatedDate(Instant.now());
                lpsList.add(labProvServ);
            }
            lsCol.insertMany(lsList);
            lpsCol.insertMany(lpsList);
            logger.info("Total: " + i);
        } catch (Exception e) {
            logger.error("Row " + i + "/" + rowIdx + " : " + e.getMessage(), e);
        }
        logger.info("Import Lab: End");
    }

    Map<String, ObjectId> labProviderMap = new HashMap<>();

    private void loadLabProviderByDes() {
        MongoCollection<LabProvider> col = mongoDatabase.getCollection(LabProvider.COLLECTION_NAME, LabProvider.class);
        FindIterable<LabProvider> findIterable = col.find(new Document("comId", SOURCE_COMPANY_ID));
        for (LabProvider value : findIterable) {
            String des = value.getDes();
            if (value.getI18n() != null
                    && value.getI18n().containsKey("des")
                    && StringUtils.isNotBlank(value.getI18n().get("des").get("en"))) {
                des = value.getI18n().get("des").get("en");
            }
            labProviderMap.put(des, value.getId());
        }
    }

    public void importImaging() {
        logger.info("importImaging: Start");
        int i = 0;
        int colNum = 2;
        try {
            loadLabProviderByDes();

            MongoCollection<LabImaging> liCol =
                    mongoDatabase.getCollection(LabImaging.COLLECTION_NAME, LabImaging.class);
            MongoCollection<LabProvServ> lpsCol =
                    mongoDatabase.getCollection(LabProvServ.COLLECTION_NAME, LabProvServ.class);

            List<LabImaging> liList = new ArrayList<>();
            List<LabProvServ> lpsList = new ArrayList<>();

            FileInputStream file = new FileInputStream(ConfigUtil.PROP.getProperty("imagingFile"));
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                i++;
                if (i < 2) {
                    continue;
                }
                colNum = 0;
                String code = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String name = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String provider = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String cost = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String price = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String instruction = ExcelUtil.getRowValue(row, colNum);
                colNum++;
                String remarks = ExcelUtil.getRowValue(row, colNum);

                String key = row.getRowNum() + "";

                LabImaging labImaging = new LabImaging();
                labImaging.setId(ObjectId.get());
                labImaging.setDes(name.trim());
                if (StringUtils.isNotBlank(code)) {
                    labImaging.setCode(code);
                } else {
                    labImaging.setCode(StringUtils.leftPad(key, 6, "0"));
                }
                labImaging.setComId(SOURCE_COMPANY_ID);
                labImaging.setSource(SOURCE);
                labImaging.setSourceKey(key);
                labImaging.setCreatedDate(Instant.now());
                liList.add(labImaging);

                LabProvServ labProvServ = new LabProvServ();
                labProvServ.setProviderId(labProviderMap.get(provider));
                labProvServ.setImgId(labImaging.getId());
                labProvServ.setCost(Decimal128.parse(cost));
                labProvServ.setPrice(Decimal128.parse(price));
                labProvServ.setInstructions(instruction);
                labProvServ.setRemarks(remarks);
                labProvServ.setComId(SOURCE_COMPANY_ID);
                labProvServ.setSource(SOURCE);
                labProvServ.setSourceKey(key);
                labProvServ.setCreatedDate(Instant.now());
                lpsList.add(labProvServ);
            }
            liCol.insertMany(liList);
            lpsCol.insertMany(lpsList);
            logger.info("Total: " + liList.size());
            logger.info("Total: " + lpsList.size());
        } catch (Exception e) {
            logger.error("Row " + i + "/" + colNum + " : " + e.getMessage(), e);
        }
        logger.info("Import Lab: End");
    }

    public void importPathLab() {
        logger.info("start to importPathLab: " + SOURCE_COMPANY_ID);
        int i = 0;
        int rowIdx = 2;
        try {
            SOURCE += "_PATHLAB";
            ObjectId labProviderId = new ObjectId(ConfigUtil.PROP.getProperty("labProviderId"));

            MongoCollection<LabService> lsCol =
                    mongoDatabase.getCollection(LabService.COLLECTION_NAME, LabService.class);
            MongoCollection<LabProvServ> lpsCol =
                    mongoDatabase.getCollection(LabProvServ.COLLECTION_NAME, LabProvServ.class);

            List<LabService> lsList = new ArrayList<>();
            List<LabProvServ> lpsList = new ArrayList<>();

            FileInputStream file = new FileInputStream(new File(ConfigUtil.PROP.getProperty("labFile")));
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                i++;
                if (i < 2) {
                    continue;
                }
                rowIdx = 0;
                String code = ExcelUtil.getRowValue(row, rowIdx);
                rowIdx++;
                String billCode = ExcelUtil.getRowValue(row, rowIdx);
                rowIdx++;
                String name = ExcelUtil.getRowValue(row, rowIdx);
                rowIdx++;
                String price = ExcelUtil.getRowValue(row, rowIdx);
                rowIdx++;
                String cost = ExcelUtil.getRowValue(row, rowIdx);
                rowIdx++;
                String inst = ExcelUtil.getRowValue(row, rowIdx);

                String key = row.getRowNum() + "-" + billCode;

                LabService labService = new LabService();
                labService.setId(ObjectId.get());
                labService.setDes(name.trim());
                labService.setCode(code);
                labService.setComId(SOURCE_COMPANY_ID);
                labService.setSource(SOURCE);
                labService.setSourceKey(key);
                labService.setCreatedDate(Instant.now());
                lsList.add(labService);

                LabProvServ labProvServ = new LabProvServ();
                labProvServ.setProviderId(labProviderId);
                labProvServ.setServiceId(labService.getId());
                labProvServ.setCost(
                        cost.equalsIgnoreCase("Call Lab") ? Decimal128.parse("999999") : Decimal128.parse(cost));
                labProvServ.setPrice(
                        price.equalsIgnoreCase("Call Lab") ? Decimal128.parse("999999") : Decimal128.parse(price));
                labProvServ.setInstructions(inst);
                labProvServ.setRemarks(
                        cost.equalsIgnoreCase("Call Lab") || price.equalsIgnoreCase("Call Lab") ? "Call Lab" : "");
                labProvServ.setComId(SOURCE_COMPANY_ID);
                labProvServ.setSource(SOURCE);
                labProvServ.setSourceKey(key);
                labProvServ.setCreatedDate(Instant.now());
                lpsList.add(labProvServ);
            }
            lsCol.insertMany(lsList);
            lpsCol.insertMany(lpsList);
            logger.info("Total: " + i);
        } catch (Exception e) {
            logger.error("Row " + i + "/" + rowIdx + " : " + e.getMessage(), e);
        }
        logger.info("end to import");
    }

    public void importPatient() {
        logger.info("importPatient: Start");
        int i = 0;
        int columnNum = 0;
        try {
            SimpleDateFormat originalFormat = new SimpleDateFormat("M/d/yyyy");
            SimpleDateFormat targetFormat = new SimpleDateFormat("yyyy-MM-dd");

            MongoCollection<ClinicPatient> cpCol =
                    mongoDatabase.getCollection(ClinicPatient.COLLECTION_NAME, ClinicPatient.class);

            List<ClinicPatient> cpList = new ArrayList<>();
            FileInputStream file = new FileInputStream(ConfigUtil.PROP.getProperty("patientFile"));
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() < 3) {
                    continue;
                }
                columnNum = 0;
                String patientCode = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                columnNum++;
                String hkid = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String engName = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String chiName = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                Instant birthday = ExcelUtil.getRowDate(row, columnNum);
                columnNum++;
                String gender = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String phoneNum = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String email = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String resAddress = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String busAddress = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String remarks = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String insurer = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String insurPlan = ExcelUtil.getRowValue(row, columnNum);
                columnNum++;
                String insurCardNum = ExcelUtil.getRowValue(row, columnNum);

                String key = row.getRowNum() + "";

                ClinicPatient clinicPatient = new ClinicPatient();
                clinicPatient.setComId(SOURCE_COMPANY_ID);
                clinicPatient.setSource(SOURCE);
                clinicPatient.setSourceKey(key);
                clinicPatient.setCreatedDate(Instant.now());

                clinicPatient.setVerifyStatus(1);
                clinicPatient.setVerifyTime(Instant.now());
                clinicPatient.setCode(patientCode.trim());
                //                clinicPatient.setIdentifierType();
                clinicPatient.setPersonalIdentifier(hkid.trim());
                clinicPatient.setFirstName(engName.trim());
                clinicPatient.setFullName(
                        clinicPatient.getFirstName().replaceAll(" ", "").toUpperCase());
                clinicPatient.setChiFirstName(chiName.trim());
                clinicPatient.setChiFullName(clinicPatient.getChiFirstName().replaceAll(" ", ""));
                clinicPatient.setGender(gender.trim());
                clinicPatient.setPhoneNum(phoneNum.trim());
                if (birthday != null) {
                    clinicPatient.setDateOfBirth(
                            TimeUtil.parseLocalDate(birthday).toString());
                }
                clinicPatient.setAddress(resAddress);
                clinicPatient.setBusiAddress(busAddress);
                clinicPatient.setRemarkByDoctor(remarks);
                clinicPatient.setEmail(email);

                InsuranceModel insuranceModel = new InsuranceModel();
                insuranceModel.setInsurer(insurer);
                insuranceModel.setPlan(insurPlan);
                insuranceModel.setMemberNum(insurCardNum);
                clinicPatient.setInsurancePlans(Arrays.asList(insuranceModel));

                cpList.add(clinicPatient);
                i++;
            }
            cpCol.insertMany(cpList);
            logger.info("Total: " + i);
        } catch (Exception e) {
            logger.error("Row " + i + "/" + columnNum + " : " + e.getMessage(), e);
        }
        logger.info("importPatient: End");
    }

    public Instant convertToInstantFrom(String objectId) {
        return convertToDateFrom(objectId).toInstant();
    }

    public Date convertToDateFrom(String objectId) {
        return new Date(convertToTimestampFrom(objectId));
    }

    public long convertToTimestampFrom(String objectId) {
        return Long.parseLong(objectId.substring(0, 8), 16) * 1000;
    }

    /**
     * Import drug interface data from DRUG_INTF.20250115 file
     * Reads the delimited file, converts each record to MongoDB Document and saves to database
     */
    public void importDrugInterface() {
        logger.info("importDrugInterface: Start");
        int recordCount = 0;
        int batchSize = 1000;
        List<Document> documentBatch = new ArrayList<>();

        try {
            // Get MongoDB collection for drug interface data
            MongoCollection<Document> drugIntfCol = mongoDatabase.getCollection("mttdrug", Document.class);

            // Set source identifier
            String source = "20250615";
            Instant now = Instant.now();

            // Read the file
            String filePath = "src/main/resources/DRUG_INTF.20250615";
            logger.info("Reading file: " + filePath);

            try (FileReader fileReader = new FileReader(filePath);
                    java.io.BufferedReader bufferedReader = new java.io.BufferedReader(fileReader)) {

                // Read header line to get field names
                String headerLine = bufferedReader.readLine();
                if (headerLine == null) {
                    logger.error("File is empty or cannot read header line");
                    return;
                }

                // Parse header fields - assuming delimiter is ASCII character 28 (File Separator)
                String[] headerFields = headerLine.split("\t", -1);
                logger.info("Found " + headerFields.length + " header fields");

                // Read data lines
                String dataLine;
                while ((dataLine = bufferedReader.readLine()) != null) {
                    recordCount++;

                    try {
                        // Split data line by tab
                        String[] dataFields = dataLine.split("\t", -1); // -1 to preserve empty fields

                        // Create MongoDB document
                        Document drugIntfDoc = new Document();
                        drugIntfDoc.append("_id", ObjectId.get());
                        drugIntfDoc.append("source", source);
                        drugIntfDoc.append("createdDate", now);
                        drugIntfDoc.append("recordNumber", recordCount);

                        // Map each field to document
                        for (int i = 0; i < Math.min(headerFields.length, dataFields.length); i++) {
                            String fieldName = headerFields[i].trim();
                            String fieldValue = i < dataFields.length ? dataFields[i].trim() : "";

                            // Convert field names to camelCase and store values
                            String camelCaseFieldName = fieldName;

                            // Handle special field types
                            if (fieldName.contains("DATE") && StringUtils.isNotBlank(fieldValue)) {
                                try {
                                    // Try to parse date fields
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss.SSS");
                                    Date parsedDate = sdf.parse(fieldValue);
                                    drugIntfDoc.append(camelCaseFieldName, parsedDate);
                                } catch (Exception e) {
                                    // If date parsing fails, store as string
                                    drugIntfDoc.append(camelCaseFieldName, fieldValue);
                                }
                            } else {
                                drugIntfDoc.append(camelCaseFieldName, fieldValue);
                            }
                        }

                        // Add to batch
                        documentBatch.add(drugIntfDoc);

                        // Insert batch when it reaches batch size
                        if (documentBatch.size() >= batchSize) {
                            drugIntfCol.insertMany(documentBatch);
                            logger.info("Inserted batch of " + documentBatch.size() + " records. Total processed: "
                                    + recordCount);
                            documentBatch.clear();
                        }

                    } catch (Exception e) {
                        logger.error("Error processing record " + recordCount + ": " + e.getMessage(), e);
                    }
                }

                // Insert remaining documents in batch
                if (!documentBatch.isEmpty()) {
                    drugIntfCol.insertMany(documentBatch);
                    logger.info("Inserted final batch of " + documentBatch.size() + " records");
                }

            } catch (Exception e) {
                logger.error("Error reading file: " + e.getMessage(), e);
            }

            logger.info("Total records processed: " + recordCount);

        } catch (Exception e) {
            logger.error("Error in importDrugInterface: " + e.getMessage(), e);
        }

        logger.info("importDrugInterface: End");
    }

    /**
     * Convert field name to camelCase format
     * Example: "REG_NO" -> "regNo", "DH_PRODUCT_NAME" -> "dhProductName"
     */
    private String convertToCamelCase(String fieldName) {
        if (StringUtils.isBlank(fieldName)) {
            return fieldName;
        }

        String[] parts = fieldName.toLowerCase().split("_");
        StringBuilder camelCase = new StringBuilder(parts[0]);

        for (int i = 1; i < parts.length; i++) {
            if (parts[i].length() > 0) {
                camelCase.append(Character.toUpperCase(parts[i].charAt(0)));
                if (parts[i].length() > 1) {
                    camelCase.append(parts[i].substring(1));
                }
            }
        }

        return camelCase.toString();
    }

    public void checkMTT() {
        logger.info("Checking MTT");
        MongoCollection<Document> originalMtt = mongoDatabase.getCollection("mttdrug_250115", Document.class);
        MongoCollection<Document> updatedMtt = mongoDatabase.getCollection("mttdrug", Document.class);

        Map<String, Document> originalMap = new HashMap<>();
        FindIterable<Document> originalMttFi = originalMtt
                .find(new Document("HK_REGISTRATION_NUMBER_DEFAULT_MAPPING", "Y"))
                .projection(new Document("REG_NO", 1)
                        .append("ACTIVE_INGREDIENTS", 1)
                        .append("INGREDIENTS", 1)
                        .append("ALLERGEN_GROUP", 1));
        for (Document document : originalMttFi) {
            document.remove("_id");
            document.remove("source");
            document.remove("createdDate");
            document.remove("recordNumber");
            document.remove("DH_PRODUCT_NAME_LAST_UPDATED");
            originalMap.put(document.getString("REG_NO"), document);
        }

        Map<String, Document> updatedMap = new HashMap<>();
        FindIterable<Document> updatedMttFi = updatedMtt
                .find(new Document("HK_REGISTRATION_NUMBER_DEFAULT_MAPPING", "Y"))
                .projection(new Document("REG_NO", 1).append("ALLERGEN_GROUP", 1));
        for (Document document : updatedMttFi) {
            document.remove("_id");
            document.remove("source");
            document.remove("createdDate");
            document.remove("recordNumber");
            document.remove("DH_PRODUCT_NAME_LAST_UPDATED");
            updatedMap.put(document.getString("REG_NO"), document);
        }

        originalMap.entrySet().stream()
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue))
                .forEach((k, v) -> {
                    if (!updatedMap.containsKey(k)) {
                        logger.info("Not found: " + k);
                    } else {
                        Document updatedDoc = updatedMap.get(k);
                        for (Entry<String, Object> entry : v.entrySet()) {
                            if (!updatedDoc.containsKey(entry.getKey())) {
                                //                        logger.info("Not found: " + k + " - " + entry.getKey());
                                continue;
                            }
                            if (!updatedDoc.get(entry.getKey()).equals(entry.getValue())) {
                                logger.info("Not match: " + k + " - " + entry.getKey() + " - " + entry.getValue()
                                        + " - " + updatedDoc.get(entry.getKey()));
                                logger.info(entry.getValue());
                                logger.info(updatedDoc.get(entry.getKey()));
                                logger.info("____________");
                            }
                        }
                    }
                });
        logger.info("Done");
    }
}
